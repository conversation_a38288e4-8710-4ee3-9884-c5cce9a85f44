$(() => {
  $('link[href="https://recoverit.wondershare.com/assets/app.bundle.css"]').remove();
  if (window.innerWidth > 1280) {
    const situationTabs = ["#college-student-tab", "#project-manager-tab", "#lawyer-tab", "#financial-analyst-tab", "#architecture-manager-tab"];
    const swiperSituation = new Swiper("#swiper-situation", {
      slidesPerView: 1,
      spaceBetween: 10,
      loop: true,
      effect: "fade",
      autoplay: {
        delay: 3500,
        disableOnInteraction: false,
      },
      pagination: {
        el: ".swiper-pagination",
      },
      on: {
        slideChange: function () {
          const currentSlide = this.realIndex;
          // 激活对应的tab
          $(situationTabs[currentSlide]).tab("show");
          // 展开对应的collapse
          $(situationTabs[currentSlide].replace("-tab", "-collapse")).collapse("show");
        },
      },
    });

    // 点击banner区域的tab项时切换到对应的swiper slide
    $(".part-banner .content-right .nav-item").on("click", function () {
      const currentSlide = $(this).index();
      // 切换到对应的slide
      swiperSituation.slideToLoop(currentSlide);
    });
  } else {
    const swiperSituationMobile = new Swiper("#swiper-situation-mobile", {
      slidesPerView: 1,
      spaceBetween: 10,
      loop: true,
      effect: "fade",
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      pagination: {
        el: ".swiper-pagination",
      },
    });
  }

  const swiperMethods = new Swiper("#swiper-methods", {
    slidesPerView: 1,
    spaceBetween: 30,
    loop: true,
    allowTouchMove: false, // 禁止手动滑动
    autoHeight: true,

    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    navigation: {
      nextEl: ".part-methods .right-btn",
      prevEl: ".part-methods .left-btn",
    },
    on: {
      slideChange: function () {
        const currentSlide = this.realIndex;
        $(".methods-navigation-item").removeClass("active").eq(currentSlide).addClass("active");
      },
    },
  });
  $(".methods-navigation-item").on("click", function () {
    const currentSlide = $(this).index();
    swiperMethods.slideToLoop(currentSlide);
  });

  if (window.innerWidth > 1280) {
    // 大屏幕使用鼠标悬停效果
    $("#my-card-slider .swiper-slide").on("mouseenter", function () {
      $(this).addClass("is-active").siblings().removeClass("is-active");
    });
  } else {
    // 小屏幕使用轮播
    const swiperMyCard = new Swiper("#my-card-slider", {
      loop: true,
      slidesPerView: 1,
      spaceBetween: 10,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
          spaceBetween: 20,
        },
        992: {
          slidesPerView: 3,
          spaceBetween: 15,
        },
      },
    });
  }

  if (window.innerWidth > 1279) {
    $(".assetsSwiper .swiper-slide").mouseenter(function () {
      $(this).addClass("active").siblings().removeClass("active");
      $(".assetsSwiper-box").css("--assetIndex", $(this).index());
    });
  } else {
    var assetsSwiper = new Swiper("#assetsSwiper", {
      slidesPerView: 1,
      spaceBetween: 20,
      centeredSlides: true,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      loop: true,

      breakpoints: {
        768: {
          slidesPerView: 1,
          spaceBetween: 20,
          centeredSlides: true,
        },
      },
      pagination: {
        el: ".assetsSwiper-pagination",
        clickable: true,
      },
    });
  }

  if (window.innerWidth < 992) {
    const swiperKeys = new Swiper("#swiper-keys", {
      slidesPerView: 1,
      spaceBetween: 20,
      loop: true,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
          spaceBetween: 20,
        },
      },
    });
  }
});
